<template>
  <div class="puzzle-animation-test">
    <h2>PuzzleVerification 动画测试</h2>
    <div class="test-buttons">
      <a-button type="primary" @click="showPuzzle">显示滑动验证</a-button>
      <a-button @click="showPuzzleWithDelay">延迟显示验证</a-button>
    </div>
    <div class="test-info">
      <p>测试说明：</p>
      <ul>
        <li>点击"显示滑动验证"按钮，观察验证组件的进入动画</li>
        <li>完成验证或点击关闭按钮，观察验证组件的离开动画</li>
        <li>验证动画结束后组件应该立即销毁，不会再次显示</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import { Button } from 'ant-design-vue';
  import { PuzzleVerification } from '@/components/Verify';

  const AButton = Button;
  const puzzleInstance = ref(null);

  function showPuzzle() {
    puzzleInstance.value = PuzzleVerification({
      success: () => {
        console.log('验证成功！');
        // 验证成功后会自动触发关闭动画
      },
      cancel: () => {
        console.log('验证取消');
        // 取消时会自动触发关闭动画
      },
      close: () => {
        console.log('验证组件关闭');
        // 组件关闭时的回调
        if (puzzleInstance.value) {
          puzzleInstance.value.close();
          puzzleInstance.value = null;
        }
      },
    });
  }

  function showPuzzleWithDelay() {
    setTimeout(() => {
      showPuzzle();
    }, 1000);
  }
</script>

<style lang="less" scoped>
  .puzzle-animation-test {
    padding: 20px;
    max-width: 600px;
    margin: 0 auto;

    h2 {
      text-align: center;
      margin-bottom: 30px;
      color: #1890ff;
    }

    .test-buttons {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-bottom: 30px;
    }

    .test-info {
      background: #f5f5f5;
      padding: 20px;
      border-radius: 8px;
      border-left: 4px solid #1890ff;

      p {
        margin: 0 0 10px 0;
        font-weight: bold;
        color: #333;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin-bottom: 8px;
          color: #666;
          line-height: 1.5;
        }
      }
    }
  }
</style>
