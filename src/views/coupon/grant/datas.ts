import { BasicColumn, FormSchema } from '@/components/Table';
import { get } from 'lodash-es';
import { CouponStateArray } from '@/maps/coupon';
import { showToBadge, showToTag } from '@/components/RenderVnode';
import { DescItem } from '@/components/Description';
import { apiCategoryList } from '@/api/operation/category';
import { EducationArray } from '@/maps/member';
import dayjs from 'dayjs';

export const basicColumns: BasicColumn[] = [
  {
    title: '消费券名称',
    dataIndex: 'title',
  },
  {
    title: '消费券码',
    dataIndex: 'id',
  },
  {
    title: '所属分类',
    dataIndex: 'category',
    customRender: ({ record }) => {
      return get(record, 'operationCategory.name', '--');
    },
  },
  {
    title: '支持的商户类别',
    dataIndex: 'storeType',
    customRender: ({ record }) => {
      return get(record, 'relevancyCouponStores', [])
        ?.map((item) => item.storeCategoryName)
        .join(',');
    },
  },
  {
    title: '消费券额度',
    dataIndex: 'price',
    customRender: ({ record }) => {
      return `满${get(record, 'threshold')}减${get(record, 'price')}`;
    },
  },
  {
    title: '有效期',
    dataIndex: 'beginDate',
    customRender: ({ record }) => {
      return `${get(record, 'beginDate')}至${get(record, 'endDate')}`;
    },
  },
  {
    title: '发放数量',
    dataIndex: 'totalCount',
  },
  {
    title: '状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({
        text,
        arr: CouponStateArray,
      });
    },
  },
  {
    title: '发放数量',
    dataIndex: 'count',
  },
  {
    title: '领取数量',
    dataIndex: 'receiveCount',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '发布时间',
    dataIndex: 'createTime',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'id',
    label: '消费券码',
    component: 'Input',
    show: false,
  },
  {
    field: 'storeCategoryIdList',
    label: '支持商户类别',
    component: 'ApiSelect',
    required: true,
    defaultValue: [],
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'STORE' });
      },
      mode: 'multiple',
      labelField: 'name',
      valueField: 'id',
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
    // transformGetTo: 'toArr',
    // transformSetTo: 'toStr',
  },
  {
    field: 'title',
    label: '优惠券名称',
    component: 'Input',
    required: true,
  },

  {
    field: 'categoryId',
    label: '所属分类',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'COUPON' });
      },
      labelField: 'name',
      valueField: 'id',
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: 'threshold',
    label: '满',
    component: 'InputNumber',
    componentProps: ({ formActionType, formModel }) => {
      return {
        min: 1,
        max: 99999,
        // 不支持小数点
        precision: 0,
        style: {
          width: '100%',
        },
        onChange: () => {
          if (get(formModel, 'price')) {
            setTimeout(() => {
              formActionType.validateFields(['price']);
            }, 1);
          }
        },
      };
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          message: '请输入满减金额',
        },
        {
          validator: (_, value) => {
            if (value && values.price && value <= values.price) {
              return Promise.reject('满减金额不能大于或等于优惠券金额');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
  {
    field: 'price',
    label: '减',
    component: 'InputNumber',
    componentProps: ({ formActionType, formModel }) => {
      return {
        min: 1,
        max: 99999,
        style: {
          width: '100%',
        },
        onChange: () => {
          if (get(formModel, 'threshold')) {
            setTimeout(() => {
              formActionType.validateFields(['threshold']);
            }, 1);
          }
        },
        // 不支持小数点
        precision: 0,
      };
    },
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          message: '请输入满减金额',
        },
        {
          validator: (_, value) => {
            if (value && values.threshold && value >= values.threshold) {
              return Promise.reject('满减金额不能大于或等于优惠券金额');
            }
            return Promise.resolve();
          },
        },
      ];
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: '[beginDate,endDate]',
    label: '有效期',
    component: 'RangePicker',
    required: true,
    componentProps: {
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
      // 不允许选择今天之前的日期 使用 dayjs 当天是可选的
      disabledDate: (current) => {
        return current && current.isBefore(dayjs().startOf('day'));
      },
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: 'count',
    label: '最大发放数量',
    required: true,
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 99999,
      style: {
        width: '100%',
      },
      // 不支持小数点
      precision: 0,
    },
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'categoryId',
    label: '消费券分类',
    component: 'ApiSelect',
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'COUPON' });
      },
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'title',
    label: '消费券名称',
    component: 'Input',
  },
  {
    field: 'code',
    label: '消费券码',
    component: 'Input',
    componentProps: {
      maxlength: 40,
    },
  },
  {
    field: 'state',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: CouponStateArray,
    },
  },
];

export const basicDesc: DescItem[] = [
  {
    label: '消费券名称',
    field: 'title',
  },
  {
    label: '消费券码',
    field: 'id',
  },
  {
    label: '消费券面额',
    field: 'price',
    render: (_, record) => {
      return `满${get(record, 'threshold')}减${get(record, 'price')}`;
    },
  },
  {
    label: '分类',
    field: 'a',
    render(_, record) {
      return get(record, 'operationCategory.name', '--');
    },
  },
  {
    label: '有效期',
    field: 'b',
    render: (_, record) => {
      return `${get(record, 'beginDate')}至${get(record, 'endDate')}`;
    },
  },
  {
    label: '状态',
    field: 'state',
    render: (text) =>
      showToBadge({
        text,
        arr: CouponStateArray,
      }),
  },
  {
    label: '发布时间',
    field: 'createTime',
  },
];

export const searchUserSchema: FormSchema[] = [
  {
    field: 'searchWord',
    label: '用户姓名',
    component: 'Input',
  },
  {
    field: 'education',
    label: '学历',
    component: 'Select',
    componentProps: {
      options: EducationArray,
    },
  },
  // {
  //   field: 'categoryTypeId',
  //   label: '消费券分类',
  //   component: 'ApiSelect',
  //   componentProps: {
  //     api: () => {
  //       return apiCategoryList({ categoryType: 'COUPON' });
  //     },
  //     labelField: 'name',
  //     valueField: 'id',
  //   },
  // },
  {
    field: 'count',
    label: '已发放数量',
    component: 'InputZero',
    defaultValue: 0,
    componentProps: {
      // 增加
      min: 0,
      max: 999999,
      precision: 0,
    },
    // ifShow({ model }) {
    //   return !!get(model, 'categoryTypeId');
    // },
  },
  {
    field: '[registerStart,registerEnd]',
    label: '注册时间',
    component: 'RangePicker',
    componentProps: {
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['注册开始', '结束时间'],
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'auth',
    label: '学历认证状态',
    component: 'Input',
    defaultValue: 'success',
    ifShow: false,
  },
];

export const userColumns: BasicColumn[] = [
  {
    title: '用户昵称',
    dataIndex: 'nickname',
  },
  {
    title: '用户姓名',
    dataIndex: 'name',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
  },
  {
    title: '学历',
    dataIndex: 'education',
    customRender({ text }) {
      return showToTag({ text, arr: EducationArray });
    },
  },
  {
    title: '领券总数',
    dataIndex: 'count',
  },
  {
    title: '注册时间',
    dataIndex: 'registerTime',
  },
];

export const selectSchemas: FormSchema[] = [
  {
    field: 'count',
    label: '每人发放数量',
    component: 'InputNumber',
    required: true,
    componentProps: {
      style: {
        width: '100%',
      },
      min: 1,
      // 不支持小数点
      precision: 0,
    },
  },
];
