import { BasicColumn, FormSchema } from '@/components/Table';
import { ActivityStatusStateArray } from '@/maps/coupon';
import { showToBadge } from '@/components/RenderVnode';
import { transformRangePicker } from '@/utils/formFn';
import dayjs from 'dayjs';
import { get } from 'lodash-es';
import { apiCategoryList } from '@/api/operation/category';

export const basicColumns: BasicColumn[] = [
  {
    title: '活动名称',
    dataIndex: 'name',
  },
  {
    title: '有效期',
    dataIndex: 'beginTime',
    customRender: ({ record }) => {
      if (!record.beginTime || !record.endTime) {
        return '--';
      }
      // 格式化输出时间 YYYY-MM-DD
      return `${dayjs(record.beginTime).format('YYYY-MM-DD')} ~ ${dayjs(record.endTime).format(
        'YYYY-MM-DD',
      )}`;
    },
  },
  {
    title: '每人限制领券次数',
    dataIndex: 'perCapita',
    customRender: ({ text }) => {
      return `${text || '--'} 次`;
    },
  },
  {
    title: '支持领券人数',
    dataIndex: 'peopleCount',
    customRender: ({ text }) => {
      return `${text || '--'} 人`;
    },
  },
  {
    title: '已领券人数',
    dataIndex: 'count',
    customRender: ({ text }) => {
      return `${text || '--'} 人`;
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      return showToBadge({
        text,
        arr: ActivityStatusStateArray,
      });
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'name',
    label: '活动名称',
    component: 'Input',
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: ActivityStatusStateArray,
    },
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'name',
    fields: ['id'],
    label: '活动名称',
    component: 'Input',
    required: true,
  },
  {
    field: '[beginTime,endTime]',
    label: '有效期',
    component: 'RangePicker',
    required: true,
    componentProps: {
      showTime: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      style: {
        width: '100%',
      },
    },
    valueFormat: transformRangePicker,
  },
  {
    field: 'count',
    label: '每人限制领券次数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 99999,
      style: {
        width: '100%',
      },
      // 不支持小数点
      precision: 0,
    },
  },
  {
    field: 'peopleCount',
    label: '支持领券人数',
    component: 'InputNumber',
    required: true,
    componentProps: {
      min: 1,
      max: 99999,
      style: {
        width: '100%',
      },
      // 不支持小数点
      precision: 0,
    },
  },
];

// 配置消费券列表
export const columnsDrawer: BasicColumn[] = [
  {
    title: '消费券名称',
    dataIndex: 'title',
  },
  {
    title: '支持的商户类别',
    dataIndex: 'storeType',
    customRender: ({ record }) => {
      return get(record, 'relevancyCouponStores', [])
        ?.map((item) => item.storeCategoryName)
        .join(',');
    },
  },
  {
    title: '消费券码',
    dataIndex: 'id',
  },
  {
    title: '所属分类',
    dataIndex: 'category',
    customRender: ({ record }) => {
      return get(record, 'operationCategory.name', '--');
    },
  },
  {
    title: '消费券额度',
    dataIndex: 'price',
    customRender: ({ record }) => {
      return `满${get(record, 'threshold')}减${get(record, 'price')}`;
    },
  },
  {
    title: '每人每次支持领取数量',
    dataIndex: 'perCapita',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
];

// 配置消费券搜索项
export const searchSchemaDrawer: FormSchema[] = [
  {
    field: 'title',
    label: '消费券名称',
    component: 'Input',
  },
  {
    field: 'code',
    label: '消费券码',
    component: 'Input',
    componentProps: {
      maxlength: 40,
    },
  },
];

// 配置消费券表单
export const schemaDrawer: FormSchema[] = [
  {
    field: 'storeCategoryIdList',
    label: '支持商户类别',
    component: 'ApiSelect',
    required: true,
    defaultValue: [],
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'STORE' });
      },
      mode: 'multiple',
      labelField: 'name',
      valueField: 'id',
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: 'title',
    label: '优惠券名称',
    fields: ['activityId', 'id', 'type'],
    component: 'Input',
    required: true,
  },

  {
    field: 'categoryId',
    label: '所属分类',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'COUPON' });
      },
      labelField: 'name',
      valueField: 'id',
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: 'threshold',
    label: '满',
    component: 'InputNumber',
    componentProps: ({ formActionType, formModel }) => {
      return {
        min: 1,
        max: 99999,
        // 不支持小数点
        precision: 0,
        style: {
          width: '100%',
        },
        onChange: () => {
          if (get(formModel, 'price')) {
            setTimeout(() => {
              formActionType.validateFields(['price']);
            }, 1);
          }
        },
      };
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          message: '请输入满减金额',
        },
        {
          validator: (_, value) => {
            if (value && values.price && value <= values.price) {
              return Promise.reject('满减金额不能大于或等于优惠券金额');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
  {
    field: 'price',
    label: '减',
    component: 'InputNumber',
    componentProps: ({ formActionType, formModel }) => {
      return {
        min: 1,
        max: 99999,
        style: {
          width: '100%',
        },
        onChange: () => {
          if (get(formModel, 'threshold')) {
            setTimeout(() => {
              formActionType.validateFields(['threshold']);
            }, 1);
          }
        },
        // 不支持小数点
        precision: 0,
      };
    },
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          message: '请输入满减金额',
        },
        {
          validator: (_, value) => {
            if (value && values.threshold && value >= values.threshold) {
              return Promise.reject('满减金额不能大于或等于优惠券金额');
            }
            return Promise.resolve();
          },
        },
      ];
    },
    dynamicDisabled({ model }) {
      return !!model.id;
    },
  },
  {
    field: 'perCapita',
    label: '每人每次支持领取数量',
    required: true,
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 99999,
      style: {
        width: '100%',
      },
      // 不支持小数点
      precision: 0,
    },
  },
];
