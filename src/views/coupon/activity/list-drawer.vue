<template>
  <div>
    <SDrawer
      @register="register"
      width="80%"
      :is-ok="false"
      title="配置消费券"
      cancel-text="关闭"
      closable
    >
      <BasicTable @register="registerTable">
        <template #tableTitle>
          <a-button type="primary" @click="method.add">新增消费券</a-button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="tableAction(record)" />
          </template>
        </template>
      </BasicTable>
      <SDrawerForm @register="registerDrawer" @success="reloadTable()" />
    </SDrawer>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { useSDrawerInner, SDrawer, SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import {
    apiCouponPage,
    apiCouponAdd,
    apiCouponEdit,
    apiCouponDelete,
  } from '@/api/operation/coupon';
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { searchSchemaDrawer, schemaDrawer, columnsDrawer } from './list.schema';
  import { get, map } from 'lodash-es';

  defineEmits(['register']);
  const searchForm = reactive({
    activityId: '',
  });
  const cacheRecord = ref<any>({});

  const [registerTable, { reload: reloadTable }] = useTable({
    columns: columnsDrawer,
    api: apiCouponPage,
    formConfig: {
      schemas: searchSchemaDrawer,
    },
    searchInfo: searchForm,
    useSearchForm: true,
    immediate: false,
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: schemaDrawer,
    addFn: apiCouponAdd,
    updateFn: apiCouponEdit,
    mergeData: {
      type: 'RECEIVE',
    },
  });

  const [register] = useSDrawerInner(async (data) => {
    const { record } = data;
    cacheRecord.value = record;
    searchForm.activityId = record.id;
    reloadTable && reloadTable();
  });

  const method = {
    add() {
      addDrawer({
        record: {
          activityId: cacheRecord.value.id,
        },
      });
    },
    edit(record: Recordable) {
      updateDrawer({
        record: {
          ...record,
          storeCategoryIdList: map(get(record, 'relevancyCouponStores', []) || [], (item) => {
            return item.storeCategoryId;
          }),
        },
      });
    },
    async del(record: Recordable) {
      await apiCouponDelete(record.id);
      reloadTable();
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
