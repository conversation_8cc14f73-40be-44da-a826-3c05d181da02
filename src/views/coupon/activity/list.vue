<template>
  <page-wrapper title="列表">
    <template #extra>
      <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
        新增
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawerForm" @success="reload()" />
    <ListDrawer @register="registerSDrawer" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { SDrawerForm, useSDrawerForm, useSDrawer } from '@/components/SDrawer';
  import {
    apiActivityPageInfo,
    apiActivityAdd,
    apiActivityEdit,
    apiActivityChangeStatus,
    apiActivityDelete,
  } from '@/api/operation/activity';
  import { searchSchema, basicColumns, basicSchema } from './list.schema';
  import ListDrawer from './list-drawer.vue';

  const [registerTable, { reload }] = useTable({
    api: apiActivityPageInfo,
    columns: basicColumns,
    formConfig: {
      schemas: searchSchema,
    },
    useSearchForm: true,
    actionColumn: {
      width: 240,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiActivityAdd,
    updateFn: apiActivityEdit,
  });
  const [registerSDrawer, { openDrawer }] = useSDrawer();
  const method = {
    /** 新增 */
    add: () => {
      addDrawer();
    },
    /** 更新/编辑 */
    update: async (record: Recordable) => {
      updateDrawer({
        record,
      });
    },
    /** 上架/发布/下架 */
    async operate(record: Recordable, status: string) {
      await apiActivityChangeStatus({
        id: record.id,
        status: status,
      });
      reload();
    },
    /** 删除 */
    async del(record: Recordable) {
      await apiActivityDelete(record.id);
      reload();
    },
    /** 配置消费券 */
    config(record: Recordable) {
      openDrawer({
        record,
      });
    },
  };
  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '配置消费券',
        ifShow: () => record.status === 'UNPUBLISHED',
        onClick: method.config.bind(null, record),
      },
      {
        label: '编辑',
        onClick: method.update.bind(null, record),
        ifShow: () => record.status === 'UNPUBLISHED',
      },
      {
        label: '发布',
        popConfirm: {
          title: '确定发布吗？',
          confirm: method.operate.bind(null, record, 'PUBLISHED'),
        },
        ifShow: () => record.status === 'UNPUBLISHED',
      },
      {
        label: '下架',
        popConfirm: {
          title: '确定下架吗？',
          confirm: method.operate.bind(null, record, 'REMOVE_SHELVES'),
        },
        ifShow: () => record.status === 'PUBLISHED',
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
        disabled: record.status === 'REMOVE_SHELVES',
        ifShow: () => record.status === 'UNPUBLISHED' || record.status === 'REMOVE_SHELVES',
      },
    ];
  }
</script>

<style lang="less" scoped></style>
