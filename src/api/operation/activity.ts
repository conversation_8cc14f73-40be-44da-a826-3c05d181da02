import { defHttp } from '@/utils/http/axios';

enum Api {
  Activity = '/sys/operation/activity',
  ActivityPageInfo = '/sys/operation/activity/pageInfo',
  ActivityChange = '/sys/operation/activity/change/{id}/{status}',
  ActivityId = '/sys/operation/activity/{id}',
}

/**
 * @description: 活动分页查询
 */
export const apiActivityPageInfo = (params: any) =>
  defHttp.get({ url: Api.ActivityPageInfo, params });

/**
 * @description: 添加活动
 */
export const apiActivityAdd = (data: any) =>
  defHttp.post(
    { url: Api.Activity, data },
    {
      successMessageMode: 'message',
    },
  );

/**
 * @description: 修改活动
 */
export const apiActivityEdit = (data: any) =>
  defHttp.put(
    { url: Api.Activity, data },
    {
      successMessageMode: 'message',
    },
  );

/**
 * @description: 删除活动
 */
export const apiActivityDelete = (id: any) =>
  defHttp.delete(
    { url: Api.ActivityId.replace('{id}', id) },
    {
      successMessageMode: 'message',
    },
  );

/**
 * @description: 活动上下架
 */
export const apiActivityChangeStatus = ({ id, status }: { id: number; status: string }) =>
  defHttp.put(
    { url: Api.ActivityChange.replace('{id}', String(id)).replace('{status}', status) },
    {
      successMessageMode: 'message',
    },
  );
