import { withInstall } from '@/utils';
import basicDragVerify from './src/DragVerify.vue';
import rotateDragVerify from './src/ImgRotate.vue';
import puzzleVerification from './src/PuzzleVerification.vue';
import { createFuncComp } from '@/utils/compFn';

export const BasicDragVerify = withInstall(basicDragVerify);
export const RotateDragVerify = withInstall(rotateDragVerify);
export const PuzzleVerification = createFuncComp(
  withInstall(puzzleVerification),
  '.vue-puzzle-vcode',
);
export * from './src/typing';
